import pyautogui
import re
import sys
import time

from google import genai
from .tools import ComputerTool


def analyze_response(response):
    """
    Analyze the response from the Gemini model and return the appropriate action.
    """
    answer = list(response.text)
    tools = []
    tool = ""
    in_response = 3
    while answer:
        letter = answer.pop(0)
        if letter == "<":
          if in_response == 3:
              in_response = 1
          elif answer[0] != "/":
              return []
        if in_response < 3:
            tool += letter
            if letter == ">":
                in_response += 1
                if in_response == 3:
                    tools.append(tool)
                    tool = ""
    print("\n", tools, "\n")
    return tools
                

def main():
    assert len(sys.argv) > 1, "You need to provide an instruction for the agent with your start command."
    goal = sys.argv[1]
    print(goal)
    time.sleep(1)

    client = genai.Client()
    computer = ComputerTool()

    #<spotlight>search</spotlight> Use this to use the MacOS Spotlight search for opening tools like browsers which is more reliable than clicking.
    #<spotlight>"name of the tool"</spotlight> Use this to use the MacOS Spotlight search for opening tools like browsers which is more reliable than clicking.

    prompt = """
    You are an assistant to control my computer. 
    Please help me to complete the goal I provide you.
    You only ask questions when necessary as the user can only provide one initial goal. Try to compelete with the most reasonable option in case not sure.
    You should go step by step and explain what you are doing in every step before you do it.
    Always complete the task and don't stop in the middle of the task.
    You have an max of 50 steps to complete the task.
    If there already is an application opened that would help with the task use it instead of opening another one.

    You can use a set of tools to complete the task. Every message can contain multiple of such tools.
    If you want to type and press enter you should first only type and than press enter later after you got feedback on the typing.
    <screenshot></screenshot> Use this to ask for a screenshot.
    <click>xmin, ymin, xmax, ymax</click> Use this and provide a bounding box of the target element in [xmin, ymin, xmax, ymax] format. Coordinates are normalized between 0 and 1000.
    <right_click>xmin, ymin, xmax, ymax</right_click> Use this and provide a bounding box of the target element in [xmin, ymin, xmax, ymax] format. Coordinates are normalized between 0 and 1000 .
    <spotlight>name of the tool</spotlight> Use this to use the MacOS Spotlight search for searching and opening applications which is more reliable than clicking.
    <exit> Use this when you are done with the task to end the agent and stop the program.
    <help>your question</help> Use this to ask the user a question. The user can than provide an answer which will be used as context for the next steps.
    <type>the text you want to type</type> Use this to provide a text you want to type on the keyboard. Only use it after you selected a textfield in which you can type.
    <keys>keyboard key, keyboard key, keyboard key</keys> Use this to press one or multiple keyboard key. You can use this to use commands.

    The following are valid keyboard keys you could press:
    ['\t', '\n', '\r', ' ', '!', '"', '#', '$', '%', '&', "'", '(',
    ')', '*', '+', ',', '-', '.', '/', '0', '1', '2', '3', '4', '5', '6', '7',
    '8', '9', ':', ';', '<', '=', '>', '?', '@', '[', '\\', ']', '^', '_', '`',
    'a', 'b', 'c', 'd', 'e','f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o',
    'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~',
    'accept', 'add', 'alt', 'altleft', 'altright', 'apps', 'backspace',
    'browserback', 'browserfavorites', 'browserforward', 'browserhome',
    'browserrefresh', 'browsersearch', 'browserstop', 'capslock', 'clear',
    'convert', 'ctrl', 'ctrlleft', 'ctrlright', 'decimal', 'del', 'delete',
    'divide', 'down', 'end', 'enter', 'esc', 'escape', 'execute', 'f1', 'f10',
    'f11', 'f12', 'f13', 'f14', 'f15', 'f16', 'f17', 'f18', 'f19', 'f2', 'f20',
    'f21', 'f22', 'f23', 'f24', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9',
    'final', 'fn', 'hanguel', 'hangul', 'hanja', 'help', 'home', 'insert', 'junja',
    'kana', 'kanji', 'launchapp1', 'launchapp2', 'launchmail',
    'launchmediaselect', 'left', 'modechange', 'multiply', 'nexttrack',
    'nonconvert', 'num0', 'num1', 'num2', 'num3', 'num4', 'num5', 'num6',
    'num7', 'num8', 'num9', 'numlock', 'pagedown', 'pageup', 'pause', 'pgdn',
    'pgup', 'playpause', 'prevtrack', 'print', 'printscreen', 'prntscrn',
    'prtsc', 'prtscr', 'return', 'right', 'scrolllock', 'select', 'separator',
    'shift', 'shiftleft', 'shiftright', 'sleep', 'space', 'stop', 'subtract', 'tab',
    'up', 'volumedown', 'volumemute', 'volumeup', 'win', 'winleft', 'winright', 'yen',
    'command', 'option', 'optionleft', 'optionright']

    1. Plan the task step by step before proceding and explain what you are doing in every step. Make sure not to use xml tags to not trigger the tool usage.
    2. Follow the instructions step by step and use the tools only when you are sure that you need them.
    3. Make sure when you used a tool, check that you get the correct response from the tool with the acompanying screenshot before you proceed with the next step.
    4. If you are not sure about the next step, take a screenshot and analyze it to decide what to do next.
    5. When you use multiple tools in one step, make sure to use them in the correct order and explain what you are doing. 
    6. Make sure you only use "<>" when you want to use a tool. Don't use them in your explanations or when you are not using a tool.
    """

    screen_width, screen_height = computer.take_screenshot().size
    click_width, click_height = pyautogui.size()

    print(screen_width, screen_height)
    print(click_width, click_height)
    prompt += f"\nGoal: {goal}"

    messages = [prompt]
    end_program = False
    for i in range(50):
        for i in range(10):
            try:
                time.sleep(1)
                response = client.models.generate_content(
                    model='gemini-2.0-flash-thinking-exp-01-21', contents=messages
                )
                print(response.text)
                break
            except genai.errors.ClientError as e:
                print(f"Client error occurred: {e}")
                time.sleep(10)
            except genai.errors.APIError as e:
                print(f"API error occurred: {e}")
                time.sleep(5)
            except genai.errors.ServerError as e:
                print(f"Server error occurred: {e}")
                time.sleep(5)
            if i == 4:
                print("Failed to get a response after multiple attempts.")
                end_program = True
                break
        
        if end_program:
            end_program = False
            continue

        tool_use = analyze_response(response)

        for tool in tool_use:
            if "<exit>" in tool:
                end_program = True
                break

            if "<help>" in tool:
                match = re.search(r'<help>(.*)</help>', tool)
                if match:
                    user_answer = input("Agent: " + match.groups())
                    messages.append(f"User Answer: {user_answer}")
                else:
                    print(f"error when trying to parse help request from {tool}")
                    messages.append(f"error when trying to parse help request from {tool}" 
                                    + "<help>your question</help> Use this to ask the user a question. The user can than provide an answer which will be used as context for the next steps.")
                continue

            if "<screenshot>" in tool:
                messages.append(computer.take_screenshot())
                continue

            if "<click>" in tool:
                match = re.search(r'<click>(\d+),\s*(\d+),\s*(\d+),\s*(\d+)</click>', tool)
                if match:
                    xmin, ymin, xmax, ymax = map(int, match.groups())
                    # ymin, xmin, ymax, xmax = map(int, match.groups())
                    xmin = int((xmin / 1000) * screen_width)
                    ymin = int((ymin / 1000) * screen_height)
                    xmax = int((xmax / 1000) * screen_width)
                    ymax = int((ymax / 1000) * screen_height)

                    ymin = (ymin * (click_height / screen_height))
                    xmin = (xmin * (click_width / screen_width))
                    ymax = (ymax * (click_height / screen_height))
                    xmax = (xmax * (click_width / screen_width))

                    x = (xmin + xmax) // 2
                    y = (ymin + ymax) // 2
                    computer.click(x, y)
                    messages.append(f"Successfully clicked {x}, {y}. The next Screenshot shows the state after the loading. Please verify whether the mouse hit the target element.")
                    time.sleep(1)
                    messages.append(computer.take_screenshot())
                    continue
                else:
                    print(f"error when trying to parse click coordinates from {tool}")
                    messages.append(f"error when trying to parse click coordinates from {tool}" 
                                    + "<click>xmin, ymin, xmax, ymax</click> Use this and provide a bounding box of the target element in [xmin, ymin, xmax, ymax] format. Coordinates are normalized between 0 and 1000.")
                    break
            
            if "<right_click>" in tool:
                match = re.search(r'<click>(\d+),\s*(\d+),\s*(\d+),\s*(\d+)</click>', tool)
                if match:
                    xmin, ymin, xmax, ymax = map(int, match.groups())
                    # ymin, xmin, ymax, xmax = map(int, match.groups())
                    xmin = int((xmin / 1000) * screen_width)
                    ymin = int((ymin / 1000) * screen_height)
                    xmax = int((xmax / 1000) * screen_width)
                    ymax = int((ymax / 1000) * screen_height)

                    ymin = (ymin * (click_height / screen_height))
                    xmin = (xmin * (click_width / screen_width))
                    ymax = (ymax * (click_height / screen_height))
                    xmax = (xmax * (click_width / screen_width))

                    x = (xmin + xmax) // 2
                    y = (ymin + ymax) // 2
                    computer.right_click(x, y)
                    messages.append(f"Successfully clicked {x}, {y}. The next Screenshot shows the state after the loading. Please verify whether the mouse hit the target element.")
                    time.sleep(1)
                    messages.append(computer.take_screenshot())
                    continue
                else:
                    print(f"error when trying to parse click coordinates from {tool}")
                    messages.append(f"error when trying to parse click coordinates from {tool}" 
                                    + "<right_click>xmin, ymin, xmax, ymax</right_click> Use this and provide a bounding box of the target element in [xmin, ymin, xmax, ymax] format. Coordinates are normalized between 0 and 1000.")
                    break

            if "<spotlight>" in tool:
                pattern = r"<spotlight>(.*?)</spotlight>"
                match = re.search(pattern, tool)
                result = match.group(1) if match else None
                if result is not None:
                    computer.open_spotlight(result)
                    messages.append(f"Opened {result} with spotlight.")
                    time.sleep(1)
                    messages.append(computer.take_screenshot())
                    continue
                else:
                    print(f"error when using spotlight with {tool}")
                    messages.append(f"error when using spotlight with {tool}" + 
                                    "<spotlight>name of the tool</spotlight> Use this to use the MacOS Spotlight search for searching and opening applications which is more reliable than clicking.")
                    break

            if "<type>" in tool:
                pattern = r"<type>(.*?)</type>"
                match = re.search(pattern, tool)
                result = match.group(1) if match else None
                if result is not None:
                    computer.typewrite(result)
                    messages.append(f"Typed {result} on the keyboard.")
                    time.sleep(0.5)
                    messages.append(computer.take_screenshot())
                    continue
                else:
                    print(f"error when trying to type {tool}")
                    messages.append(f"error when trying to type {tool}\nInstruction: " +
                                    "<type>the text you want to type</type> Use this to provide a text you want to type on the keyboard. Only use it after you selected a textfield in which you can type.")
                    break

            if "<keys>" in tool:
                pattern = r"<keys>([\w\s]+(?:,\s*[\w\s]+)*)</keys>"
                match = re.search(pattern, tool)
                result = match.group(1) if match else None
                if result is not None:
                    computer.press_keys(result.split(", "))
                    messages.append(f"Pressed {result} on the keyboard.")
                    time.sleep(1)
                    messages.append(computer.take_screenshot())
                    continue
                else:
                    print(f"error when trying to press {tool}")
                    messages.append(f"error when trying to press {tool}\nInstruction: " + 
                                    "<keys>keyboard key, keyboard key, keyboard key</keys> Use this to press one or multiple keyboard key. You can use this to use commands.")
                    break
        if end_program:
            break
