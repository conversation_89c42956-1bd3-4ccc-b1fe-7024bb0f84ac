"""Action executor with feedback and validation."""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType
from .computer_controller import Computer<PERSON>ontroller
from ..vision.visual_processor import VisualProcessor


class ActionStatus(Enum):
    """Status of an action execution."""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ActionResult:
    """Result of an action execution."""
    action_id: str
    action_type: str
    status: ActionStatus
    success: bool
    message: str
    execution_time: float
    data: Dict[str, Any]
    timestamp: float


class ActionExecutor:
    """Executes actions with feedback and validation."""
    
    def __init__(self, computer_controller: ComputerController,
                 visual_processor: VisualProcessor):
        self.computer_controller = computer_controller
        self.visual_processor = visual_processor
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        # Action tracking
        self.active_actions: Dict[str, ActionResult] = {}
        self.action_history: List[ActionResult] = []
        self.max_history_size = 100
        
        # Action counter for unique IDs
        self._action_counter = 0
    
    async def execute_action(self, action_type: str, parameters: Dict[str, Any],
                           validate: bool = True, timeout: float = 30.0) -> ActionResult:
        """Execute an action with validation and feedback."""
        action_id = self._generate_action_id()
        start_time = time.time()
        
        # Create action result
        result = ActionResult(
            action_id=action_id,
            action_type=action_type,
            status=ActionStatus.PENDING,
            success=False,
            message="",
            execution_time=0.0,
            data=parameters.copy(),
            timestamp=start_time
        )
        
        self.active_actions[action_id] = result
        
        try:
            self.logger.info(f"Executing action {action_id}: {action_type}")
            
            # Update status
            result.status = ActionStatus.EXECUTING
            
            # Get visual context before action (if validation enabled)
            context_before = None
            if validate:
                context_before = self.visual_processor.get_current_context()
            
            # Execute the action
            success = await self._execute_specific_action(action_type, parameters)
            
            # Update result
            result.success = success
            result.execution_time = time.time() - start_time
            
            if success:
                result.status = ActionStatus.COMPLETED
                result.message = f"Action {action_type} completed successfully"
                
                # Validate action effect if requested
                if validate:
                    validation_result = await self._validate_action_effect(
                        action_type, parameters, context_before, timeout
                    )
                    result.data["validation"] = validation_result
                    
                    if not validation_result["success"]:
                        result.message += f" (Validation warning: {validation_result['message']})"
            else:
                result.status = ActionStatus.FAILED
                result.message = f"Action {action_type} failed"
            
            # Emit completion event
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED if success else EventType.ACTION_FAILED,
                {
                    "action_id": action_id,
                    "action_type": action_type,
                    "success": success,
                    "execution_time": result.execution_time
                },
                "action_executor"
            )
            
        except asyncio.TimeoutError:
            result.status = ActionStatus.FAILED
            result.message = f"Action {action_type} timed out after {timeout}s"
            result.execution_time = time.time() - start_time
            
            self.logger.error(f"Action {action_id} timed out")
            
        except Exception as e:
            result.status = ActionStatus.FAILED
            result.message = f"Action {action_type} failed with error: {str(e)}"
            result.execution_time = time.time() - start_time
            
            self.logger.error(f"Action {action_id} failed: {e}")
            
        finally:
            # Move to history and clean up
            self._finalize_action(action_id, result)
        
        return result
    
    async def _execute_specific_action(self, action_type: str, 
                                     parameters: Dict[str, Any]) -> bool:
        """Execute a specific action type."""
        try:
            if action_type == "click":
                return await self.computer_controller.click(
                    parameters["x"], parameters["y"],
                    parameters.get("button", "left"),
                    parameters.get("validate", True),
                    parameters.get("wait_for_change", True)
                )
            
            elif action_type == "click_element":
                return await self.computer_controller.click_element(
                    parameters["element_description"],
                    parameters.get("confidence_threshold", 0.8)
                )
            
            elif action_type == "type_text":
                return await self.computer_controller.type_text(
                    parameters["text"],
                    parameters.get("delay", None)
                )
            
            elif action_type == "press_keys":
                return await self.computer_controller.press_keys(
                    parameters["keys"],
                    parameters.get("hold_duration", 0.1)
                )
            
            elif action_type == "scroll":
                return await self.computer_controller.scroll(
                    parameters["direction"],
                    parameters.get("amount", 3),
                    parameters.get("x", None),
                    parameters.get("y", None)
                )
            
            elif action_type == "drag":
                return await self.computer_controller.drag(
                    parameters["start_x"], parameters["start_y"],
                    parameters["end_x"], parameters["end_y"],
                    parameters.get("duration", 1.0)
                )
            
            elif action_type == "spotlight":
                return await self.computer_controller.open_spotlight(
                    parameters["search_term"]
                )
            
            else:
                self.logger.error(f"Unknown action type: {action_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error executing {action_type}: {e}")
            return False
    
    async def _validate_action_effect(self, action_type: str, parameters: Dict[str, Any],
                                    context_before: Any, timeout: float) -> Dict[str, Any]:
        """Validate that an action had the expected effect."""
        validation_result = {
            "success": True,
            "message": "Action effect validated",
            "changes_detected": False,
            "validation_time": 0.0
        }
        
        start_time = time.time()
        
        try:
            # Wait for visual changes
            change_detected = False
            
            while time.time() - start_time < timeout:
                current_context = self.visual_processor.get_current_context()
                
                if current_context and context_before:
                    # Check if screen has changed
                    if current_context.timestamp > context_before.timestamp:
                        change_detected = True
                        break
                
                await asyncio.sleep(0.1)
            
            validation_result["changes_detected"] = change_detected
            validation_result["validation_time"] = time.time() - start_time
            
            # Action-specific validation
            if action_type in ["click", "click_element"]:
                if not change_detected:
                    validation_result["success"] = False
                    validation_result["message"] = "No visual change detected after click"
            
            elif action_type == "type_text":
                # For typing, we might not always see immediate visual changes
                validation_result["success"] = True
                validation_result["message"] = "Text typed (visual validation not required)"
            
            elif action_type == "scroll":
                if not change_detected:
                    validation_result["success"] = False
                    validation_result["message"] = "No visual change detected after scroll"
            
        except Exception as e:
            validation_result["success"] = False
            validation_result["message"] = f"Validation error: {str(e)}"
            validation_result["validation_time"] = time.time() - start_time
        
        return validation_result
    
    def _generate_action_id(self) -> str:
        """Generate a unique action ID."""
        self._action_counter += 1
        return f"action_{self._action_counter}_{int(time.time())}"
    
    def _finalize_action(self, action_id: str, result: ActionResult) -> None:
        """Finalize an action and move it to history."""
        # Remove from active actions
        if action_id in self.active_actions:
            del self.active_actions[action_id]
        
        # Add to history
        self.action_history.append(result)
        
        # Limit history size
        if len(self.action_history) > self.max_history_size:
            self.action_history.pop(0)
        
        self.logger.debug(f"Finalized action {action_id}: {result.status.value}")
    
    def get_action_status(self, action_id: str) -> Optional[ActionResult]:
        """Get the status of an action."""
        # Check active actions first
        if action_id in self.active_actions:
            return self.active_actions[action_id]
        
        # Check history
        for result in self.action_history:
            if result.action_id == action_id:
                return result
        
        return None
    
    def get_active_actions(self) -> List[ActionResult]:
        """Get all currently active actions."""
        return list(self.active_actions.values())
    
    def get_action_history(self, limit: int = 10) -> List[ActionResult]:
        """Get recent action history."""
        return self.action_history[-limit:] if limit > 0 else self.action_history.copy()
    
    def get_success_rate(self, action_type: Optional[str] = None, 
                        recent_count: int = 50) -> float:
        """Get success rate for actions."""
        recent_actions = self.action_history[-recent_count:]
        
        if action_type:
            recent_actions = [a for a in recent_actions if a.action_type == action_type]
        
        if not recent_actions:
            return 0.0
        
        successful_actions = [a for a in recent_actions if a.success]
        return len(successful_actions) / len(recent_actions)
    
    async def cancel_action(self, action_id: str) -> bool:
        """Cancel an active action."""
        if action_id in self.active_actions:
            result = self.active_actions[action_id]
            result.status = ActionStatus.CANCELLED
            result.message = "Action cancelled by user"
            result.execution_time = time.time() - result.timestamp
            
            self._finalize_action(action_id, result)
            
            self.logger.info(f"Cancelled action {action_id}")
            return True
        
        return False
