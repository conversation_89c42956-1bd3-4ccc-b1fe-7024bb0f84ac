"""
Gemini Computer Use - Advanced computer control using Gemini 2.0 Live API.

This package provides real-time computer control capabilities using Google's
Gemini 2.0 Live API with continuous visual processing and interactive control.

Key Features:
- Real-time screen capture and streaming
- Live visual processing and context awareness
- Advanced computer control with visual validation
- Multi-modal interaction (text, audio, visual)
- Interruption handling and priority management
- Session management with resumption capabilities
"""

from .core.config import Config, get_config, set_config
from .interaction.live_client import LiveClient

__version__ = "0.2.0"
__author__ = "Maximilian Wessendorf"

__all__ = [
    "Config",
    "get_config",
    "set_config",
    "LiveClient"
]