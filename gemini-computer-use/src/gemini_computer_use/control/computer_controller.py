"""Enhanced computer controller with visual validation."""

import asyncio
import logging
import time
from typing import Tu<PERSON>, Optional, Dict, Any
import pyautogui
import numpy as np
import cv2

from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType
from ..vision.visual_processor import VisualProcessor


class ComputerController:
    """Enhanced computer controller with visual feedback and validation."""
    
    def __init__(self, visual_processor: VisualProcessor):
        self.visual_processor = visual_processor
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        # Configure pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = self.config.control.click_delay
        
        # Screen dimensions
        self.screen_width, self.screen_height = pyautogui.size()
        
    async def click(self, x: int, y: int, button: str = "left", 
                   validate: bool = True, wait_for_change: bool = True) -> bool:
        """Click at coordinates with optional visual validation."""
        try:
            self.logger.info(f"Clicking at ({x}, {y}) with {button} button")
            
            # Get visual context before click
            context_before = None
            if validate:
                context_before = self.visual_processor.get_current_context()
            
            # Perform click
            if button == "left":
                pyautogui.click(x, y)
            elif button == "right":
                pyautogui.rightClick(x, y)
            elif button == "double":
                pyautogui.doubleClick(x, y)
            else:
                raise ValueError(f"Unsupported button type: {button}")
            
            # Wait for system to process click
            await asyncio.sleep(self.config.control.click_delay)
            
            # Emit action event
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED,
                {
                    "action": "click",
                    "coordinates": (x, y),
                    "button": button,
                    "timestamp": time.time()
                },
                "computer_controller"
            )
            
            # Visual validation
            if validate and wait_for_change:
                success = await self._validate_click_effect(context_before)
                if not success:
                    self.logger.warning(f"Click at ({x}, {y}) may not have had expected effect")
                return success
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error clicking at ({x}, {y}): {e}")
            await self.event_bus.emit(
                EventType.ACTION_FAILED,
                {
                    "action": "click",
                    "coordinates": (x, y),
                    "error": str(e)
                },
                "computer_controller"
            )
            return False
    
    async def click_element(self, element_description: str, 
                           confidence_threshold: float = 0.8) -> bool:
        """Click on an element identified by description."""
        try:
            # Find element using visual processor
            element = await self.visual_processor.find_element(element_description)
            
            if not element:
                self.logger.warning(f"Element not found: {element_description}")
                return False
            
            if element.confidence < confidence_threshold:
                self.logger.warning(f"Element confidence {element.confidence} below threshold {confidence_threshold}")
                return False
            
            # Calculate click coordinates (center of element)
            x = element.bounds["x"] + element.bounds["width"] // 2
            y = element.bounds["y"] + element.bounds["height"] // 2
            
            return await self.click(x, y)
            
        except Exception as e:
            self.logger.error(f"Error clicking element '{element_description}': {e}")
            return False
    
    async def type_text(self, text: str, delay: Optional[float] = None) -> bool:
        """Type text with optional delay between characters."""
        try:
            if delay is None:
                delay = self.config.control.type_delay
            
            self.logger.info(f"Typing text: '{text[:50]}{'...' if len(text) > 50 else ''}'")
            
            # Type with delay
            for char in text:
                pyautogui.typewrite(char)
                if delay > 0:
                    await asyncio.sleep(delay)
            
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED,
                {
                    "action": "type",
                    "text": text,
                    "length": len(text),
                    "timestamp": time.time()
                },
                "computer_controller"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error typing text: {e}")
            await self.event_bus.emit(
                EventType.ACTION_FAILED,
                {
                    "action": "type",
                    "text": text,
                    "error": str(e)
                },
                "computer_controller"
            )
            return False
    
    async def press_keys(self, keys: list, hold_duration: float = 0.1) -> bool:
        """Press multiple keys simultaneously."""
        try:
            self.logger.info(f"Pressing keys: {keys}")
            
            # Press all keys down
            for key in keys:
                pyautogui.keyDown(key)
            
            # Hold for specified duration
            await asyncio.sleep(hold_duration)
            
            # Release all keys
            for key in reversed(keys):
                pyautogui.keyUp(key)
            
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED,
                {
                    "action": "press_keys",
                    "keys": keys,
                    "timestamp": time.time()
                },
                "computer_controller"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error pressing keys {keys}: {e}")
            await self.event_bus.emit(
                EventType.ACTION_FAILED,
                {
                    "action": "press_keys",
                    "keys": keys,
                    "error": str(e)
                },
                "computer_controller"
            )
            return False
    
    async def scroll(self, direction: str, amount: int = 3, x: Optional[int] = None, 
                    y: Optional[int] = None) -> bool:
        """Scroll in the specified direction."""
        try:
            if x is None:
                x = self.screen_width // 2
            if y is None:
                y = self.screen_height // 2
            
            self.logger.info(f"Scrolling {direction} by {amount} at ({x}, {y})")
            
            if direction.lower() == "up":
                pyautogui.scroll(amount, x, y)
            elif direction.lower() == "down":
                pyautogui.scroll(-amount, x, y)
            else:
                raise ValueError(f"Unsupported scroll direction: {direction}")
            
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED,
                {
                    "action": "scroll",
                    "direction": direction,
                    "amount": amount,
                    "coordinates": (x, y),
                    "timestamp": time.time()
                },
                "computer_controller"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error scrolling {direction}: {e}")
            await self.event_bus.emit(
                EventType.ACTION_FAILED,
                {
                    "action": "scroll",
                    "direction": direction,
                    "error": str(e)
                },
                "computer_controller"
            )
            return False
    
    async def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
                  duration: float = 1.0) -> bool:
        """Drag from start to end coordinates."""
        try:
            self.logger.info(f"Dragging from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            
            pyautogui.drag(end_x - start_x, end_y - start_y, duration, 
                          button='left', start=(start_x, start_y))
            
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED,
                {
                    "action": "drag",
                    "start": (start_x, start_y),
                    "end": (end_x, end_y),
                    "duration": duration,
                    "timestamp": time.time()
                },
                "computer_controller"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error dragging: {e}")
            await self.event_bus.emit(
                EventType.ACTION_FAILED,
                {
                    "action": "drag",
                    "start": (start_x, start_y),
                    "end": (end_x, end_y),
                    "error": str(e)
                },
                "computer_controller"
            )
            return False
    
    async def open_spotlight(self, search_term: str) -> bool:
        """Open macOS Spotlight search."""
        try:
            self.logger.info(f"Opening Spotlight with search: '{search_term}'")
            
            # Open Spotlight (Cmd+Space)
            await self.press_keys(["command", "space"])
            await asyncio.sleep(0.5)
            
            # Type search term
            await self.type_text(search_term)
            await asyncio.sleep(0.5)
            
            # Press Enter
            pyautogui.press("enter")
            
            await self.event_bus.emit(
                EventType.ACTION_COMPLETED,
                {
                    "action": "spotlight",
                    "search_term": search_term,
                    "timestamp": time.time()
                },
                "computer_controller"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error opening Spotlight: {e}")
            await self.event_bus.emit(
                EventType.ACTION_FAILED,
                {
                    "action": "spotlight",
                    "search_term": search_term,
                    "error": str(e)
                },
                "computer_controller"
            )
            return False
    
    async def _validate_click_effect(self, context_before, timeout: float = 2.0) -> bool:
        """Validate that a click had the expected effect."""
        if not context_before:
            return True  # Can't validate without before context
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_context = self.visual_processor.get_current_context()
            
            if current_context and current_context.timestamp > context_before.timestamp:
                # Screen has changed since the click
                return True
            
            await asyncio.sleep(0.1)
        
        return False  # No change detected within timeout
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Get screen dimensions."""
        return self.screen_width, self.screen_height
