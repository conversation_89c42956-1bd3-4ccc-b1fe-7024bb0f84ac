import pyautogui


class ComputerTool:
    def __init__(self) -> None:
        pass

    def take_screenshot(self):
        img = pyautogui.screenshot()
        return img

    def click(self, x, y):
        pyautogui.moveTo(x, y, 1)
        pyautogui.click(button="left")
    
    def right_click(self, x, y):
        pyautogui.moveTo(x, y, 1)
        pyautogui.click(button="right")

    def open_spotlight(self, search: str):
        pyautogui.keyDown("command")
        pyautogui.press("space")
        pyautogui.keyUp("command")
        pyautogui.typewrite(search)
        pyautogui.press("enter")

    def typewrite(self, text: str):
        pyautogui.typewrite(text)

    def press_keys(self, result: list[str]):
        for key in result:
            pyautogui.keyDown(key)
        for key in result:
            pyautogui.keyUp(key)
