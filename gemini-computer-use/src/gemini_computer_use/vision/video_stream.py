"""Video streaming pipeline for Live API."""

import asyncio
import logging
import base64
from typing import Optional
from google.genai import types

from .screen_capture import ScreenCapture
from ..core.session_manager import SessionManager
from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType


class VideoStream:
    """Manages video streaming to Gemini Live API."""
    
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.screen_capture = ScreenCapture()
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        self.is_streaming = False
        self.stream_task: Optional[asyncio.Task] = None
        self.frames_sent = 0
        self.bytes_sent = 0
        
    async def start_streaming(self) -> None:
        """Start video streaming to Live API."""
        if self.is_streaming:
            return
        
        self.is_streaming = True
        self.logger.info("Starting video stream to Live API")
        
        # Start screen capture
        await self.screen_capture.start_capture()
        
        # Start streaming task
        self.stream_task = asyncio.create_task(self._stream_loop())
    
    async def stop_streaming(self) -> None:
        """Stop video streaming."""
        self.is_streaming = False
        
        # Stop screen capture
        await self.screen_capture.stop_capture()
        
        # Cancel streaming task
        if self.stream_task:
            self.stream_task.cancel()
            try:
                await self.stream_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info(f"Stopped video streaming. Sent {self.frames_sent} frames ({self.bytes_sent} bytes)")
    
    async def _stream_loop(self) -> None:
        """Main streaming loop."""
        while self.is_streaming:
            try:
                # Get frame from capture
                frame_data = await self.screen_capture.get_frame()
                
                if frame_data:
                    # Send frame to Live API
                    await self._send_frame(frame_data)
                    
                    self.frames_sent += 1
                    self.bytes_sent += len(frame_data)
                    
                    # Log progress periodically
                    if self.frames_sent % 100 == 0:
                        self.logger.debug(f"Sent {self.frames_sent} frames ({self.bytes_sent} bytes)")
                
            except Exception as e:
                self.logger.error(f"Error in streaming loop: {e}")
                await asyncio.sleep(0.1)  # Brief pause before retrying
    
    async def _send_frame(self, frame_data: bytes) -> None:
        """Send a single frame to the Live API."""
        try:
            # Encode frame as base64
            encoded_frame = base64.b64encode(frame_data).decode('utf-8')
            
            # Create video blob
            video_blob = types.Blob(
                data=encoded_frame,
                mime_type="image/jpeg"  # Since we're sending JPEG frames
            )
            
            # Send to Live API
            await self.session_manager.send_realtime_input(video=video_blob)
            
        except Exception as e:
            self.logger.error(f"Error sending frame to Live API: {e}")
            raise
    
    async def send_screen_region(self, x: int, y: int, width: int, height: int) -> None:
        """Send a specific screen region for detailed analysis."""
        try:
            # This would require implementing region capture in ScreenCapture
            # For now, we'll send the full frame with region metadata
            frame_data = await self.screen_capture.get_frame()
            
            if frame_data:
                # Add region metadata
                region_info = f"Focus on region: x={x}, y={y}, width={width}, height={height}"
                
                # Send frame with region context
                await self.session_manager.send_client_content(
                    turns={
                        "role": "user", 
                        "parts": [
                            {"text": region_info},
                            {"inline_data": {
                                "mime_type": "image/jpeg",
                                "data": base64.b64encode(frame_data).decode('utf-8')
                            }}
                        ]
                    },
                    turn_complete=True
                )
                
        except Exception as e:
            self.logger.error(f"Error sending screen region: {e}")
            raise
    
    def get_streaming_stats(self) -> dict:
        """Get streaming statistics."""
        return {
            "is_streaming": self.is_streaming,
            "frames_sent": self.frames_sent,
            "bytes_sent": self.bytes_sent,
            "current_fps": self.screen_capture.get_current_fps(),
            "buffer_size": self.screen_capture.get_buffer_size()
        }
    
    async def pause_streaming(self) -> None:
        """Temporarily pause streaming."""
        if self.stream_task:
            self.stream_task.cancel()
            self.stream_task = None
        self.logger.info("Paused video streaming")
    
    async def resume_streaming(self) -> None:
        """Resume streaming after pause."""
        if self.is_streaming and not self.stream_task:
            self.stream_task = asyncio.create_task(self._stream_loop())
            self.logger.info("Resumed video streaming")
    
    async def adjust_quality(self, quality: int) -> None:
        """Adjust streaming quality (1-100)."""
        if 1 <= quality <= 100:
            self.config.visual.compression_quality = quality
            self.logger.info(f"Adjusted streaming quality to {quality}")
        else:
            raise ValueError("Quality must be between 1 and 100")
    
    async def set_fps(self, fps: int) -> None:
        """Set target FPS for streaming."""
        if fps > 0:
            self.config.visual.fps = fps
            self.screen_capture.current_fps = fps
            self.logger.info(f"Set streaming FPS to {fps}")
        else:
            raise ValueError("FPS must be positive")
