#!/usr/bin/env python3
"""
Live API Demo - Demonstrates the new Gemini Computer Use capabilities.

This script shows how to use the refactored Live API integration for
real-time computer control with continuous visual processing.
"""

import asyncio
import logging
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from gemini_computer_use import Config, set_config, LiveClient


async def demo_basic_usage():
    """Demonstrate basic Live API usage."""
    print("🚀 Starting Gemini Computer Use Live API Demo")
    print("=" * 50)
    
    # Setup configuration
    config = Config()
    config.debug = True
    config.visual.fps = 15  # Lower FPS for demo
    config.visual.compression_quality = 75
    set_config(config)
    
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize client
    client = LiveClient()
    
    try:
        print("📡 Initializing Live API client...")
        await client.initialize()
        
        print("🎯 Starting with demo goal...")
        goal = "Take a screenshot and describe what you see on the screen"
        
        await client.start(goal)
        
        print("✅ Live API client is running!")
        print("📺 The system is now continuously monitoring the screen")
        print("🤖 Gemini can see everything in real-time and will respond accordingly")
        print("\nPress Ctrl+C to stop the demo")
        
        # Keep running for demo
        while client.is_running:
            await asyncio.sleep(1.0)
            
            # Print status every 10 seconds
            status = client.get_status()
            if status.get("video_streaming", {}).get("frames_sent", 0) % 150 == 0:  # Every ~10 seconds at 15 FPS
                print(f"📊 Status: {status}")
    
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        logging.exception("Demo error")
    finally:
        print("🔄 Stopping Live API client...")
        await client.stop()
        print("✅ Demo completed!")


async def demo_interactive_control():
    """Demonstrate interactive control capabilities."""
    print("🎮 Interactive Control Demo")
    print("=" * 30)
    
    config = Config()
    config.debug = False  # Less verbose for interactive demo
    config.visual.fps = 20
    set_config(config)
    
    client = LiveClient()
    
    try:
        await client.initialize()
        
        # Interactive goals
        goals = [
            "Open the calculator application",
            "Calculate 15 * 23 using the calculator",
            "Take a screenshot of the result"
        ]
        
        for i, goal in enumerate(goals, 1):
            print(f"\n🎯 Goal {i}: {goal}")
            
            if i == 1:
                await client.start(goal)
            else:
                await client.send_message(f"New goal: {goal}")
            
            # Wait for completion (simplified for demo)
            await asyncio.sleep(10)
            
            status = client.get_status()
            print(f"📊 Current status: {status.get('is_running', False)}")
    
    except Exception as e:
        print(f"❌ Interactive demo failed: {e}")
    finally:
        await client.stop()


async def demo_visual_monitoring():
    """Demonstrate visual monitoring and interruption handling."""
    print("👁️ Visual Monitoring Demo")
    print("=" * 30)
    
    config = Config()
    config.visual.change_detection_threshold = 0.05  # More sensitive
    config.visual.enable_adaptive_fps = True
    set_config(config)
    
    client = LiveClient()
    
    try:
        await client.initialize()
        
        goal = """
        Monitor the screen continuously and report any significant changes.
        If you see any popups, dialogs, or notifications, handle them appropriately.
        Describe what you observe in real-time.
        """
        
        await client.start(goal)
        
        print("👀 Visual monitoring active - try opening applications, dialogs, or notifications")
        print("🔄 The system will automatically detect and respond to changes")
        
        # Monitor for 30 seconds
        for i in range(30):
            await asyncio.sleep(1)
            if i % 5 == 0:
                print(f"⏱️ Monitoring... {30-i} seconds remaining")
    
    except Exception as e:
        print(f"❌ Visual monitoring demo failed: {e}")
    finally:
        await client.stop()


def main():
    """Main demo function."""
    print("🎬 Gemini Computer Use Live API Demos")
    print("=" * 40)
    
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ Error: GOOGLE_API_KEY environment variable not set")
        print("Please set your Google API key:")
        print("export GOOGLE_API_KEY=your_api_key_here")
        return
    
    demos = {
        "1": ("Basic Usage", demo_basic_usage),
        "2": ("Interactive Control", demo_interactive_control),
        "3": ("Visual Monitoring", demo_visual_monitoring)
    }
    
    print("\nAvailable demos:")
    for key, (name, _) in demos.items():
        print(f"  {key}. {name}")
    
    choice = input("\nSelect a demo (1-3) or 'all' for all demos: ").strip()
    
    if choice == "all":
        print("\n🎭 Running all demos...")
        for name, demo_func in demos.values():
            print(f"\n{'='*20} {name} {'='*20}")
            try:
                asyncio.run(demo_func())
            except KeyboardInterrupt:
                print(f"\n⏭️ Skipping to next demo...")
                continue
    elif choice in demos:
        name, demo_func = demos[choice]
        print(f"\n🎬 Running {name} demo...")
        asyncio.run(demo_func())
    else:
        print("❌ Invalid choice. Please select 1, 2, 3, or 'all'")


if __name__ == "__main__":
    main()
