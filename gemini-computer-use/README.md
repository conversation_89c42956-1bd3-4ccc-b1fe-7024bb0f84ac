# Gemini 2.0 Computer Use - Live API Edition

Advanced computer control using Gemini 2.0 Live API with real-time visual processing and continuous screen monitoring.

## 🚀 Features

### Real-Time Visual Processing
- **Continuous Screen Capture**: 30 FPS adaptive screen capture with intelligent change detection
- **Live Video Streaming**: Real-time video streaming to Gemini 2.0 Live API
- **Visual Context Awareness**: Persistent visual state tracking and analysis
- **Dynamic Decision Making**: Real-time decision making based on visual context

### Advanced Computer Control
- **Visual Element Detection**: Intelligent identification and interaction with UI elements
- **Action Validation**: Visual feedback and validation for all computer actions
- **Multi-Modal Interaction**: Support for text, audio, and visual communication
- **Interruption Handling**: Priority-based interruption management for dynamic responses

### Live API Integration
- **WebSocket Streaming**: Persistent bidirectional connection with automatic reconnection
- **Session Management**: Session resumption and context window compression for extended use
- **Tool Integration**: Advanced function calling, code execution, and Google Search grounding
- **Error Recovery**: Robust error handling with graceful degradation

## 🏗️ Architecture

The refactored architecture follows a modular, event-driven design:

```
src/gemini_computer_use/
├── core/                    # Core system components
│   ├── config.py           # Configuration management
│   ├── session_manager.py  # Live API session management
│   └── event_bus.py        # Event-driven communication
├── vision/                  # Visual processing pipeline
│   ├── screen_capture.py   # Continuous screen capture
│   ├── video_stream.py     # Video streaming to Live API
│   └── visual_processor.py # Visual context processing
├── control/                 # Computer interaction
│   ├── computer_controller.py # Enhanced computer control
│   ├── tool_manager.py     # Live API tool integration
│   └── action_executor.py  # Action execution with feedback
├── interaction/             # Live API communication
│   ├── live_client.py      # Main Live API client
│   ├── response_handler.py # Response processing
│   └── interruption_manager.py # Interruption handling
└── main.py                 # Refactored entry point
```

## 📋 Setup

This project uses PDM to manage Python dependencies. Find installation instructions here: https://pdm-project.org/en/latest/.

### Step 1: Install Dependencies

Clone the project repository and install dependencies:

```shell
git clone https://github.com/maxi-w/gemini-computer-use.git
cd gemini-computer-use
pdm install
```

### Step 2: Configure API Key

Set your Google API key as an environment variable:

```shell
export GOOGLE_API_KEY=YOUR_API_KEY
```

### Step 3: Run the Agent

Start the Live API client with a goal:

```shell
# Basic usage
pdm run start "search for cat images with google"

# Debug mode for detailed logging
pdm run start "open calculator and compute 15 * 23" --debug
```

## 🎯 How It Works

### Live Screen Feed Processing

The system continuously captures the screen at 30 FPS and streams it to the Gemini 2.0 Live API:

1. **Screen Capture**: Adaptive frame rate capture with change detection
2. **Video Encoding**: Efficient JPEG compression and base64 encoding
3. **Live Streaming**: Real-time transmission to Gemini Live API
4. **Visual Analysis**: Continuous analysis of screen content and context

### Real-Time Decision Making

The Gemini model uses the live visual feed to:

1. **Monitor State**: Track application states, UI changes, and loading processes
2. **Detect Opportunities**: Identify interactive elements and available actions
3. **Handle Interruptions**: Respond to popups, errors, and notifications
4. **Execute Actions**: Perform precise computer control with visual validation

### Interactive Control Loop

```
Live Screen Feed → Visual Analysis → Decision Making → Action Execution → Validation → Feedback
       ↑                                                                                    ↓
       ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 🛠️ Advanced Configuration

### Visual Processing Settings

```python
from gemini_computer_use import Config, set_config

config = Config()
config.visual.fps = 30                    # Capture frame rate
config.visual.compression_quality = 85    # JPEG quality (1-100)
config.visual.enable_adaptive_fps = True # Adaptive frame rate
config.visual.change_detection_threshold = 0.1  # Change sensitivity

set_config(config)
```

### Live API Configuration

```python
config.live_api.model = "gemini-2.0-flash-live-001"
config.live_api.response_modalities = ["TEXT"]  # or ["AUDIO"]
config.live_api.enable_context_compression = True
config.live_api.enable_session_resumption = True
```

## 🔧 Development

### Running Tests

```shell
pdm run pytest
```

### Code Quality

```shell
pdm run black .
pdm run flake8 .
pdm run mypy .
```

## 📊 Monitoring and Debugging

The system provides comprehensive logging and monitoring:

- **Real-time Status**: Monitor streaming stats, visual context, and action success rates
- **Debug Mode**: Detailed logging of all system components
- **Event Tracking**: Complete audit trail of all actions and decisions
- **Performance Metrics**: FPS, bandwidth usage, and response times

## 🎯 Roadmap

### Completed ✅
- [x] Real-time visual processing with Live API integration
- [x] Continuous screen capture and streaming
- [x] Advanced tool system with function calling
- [x] Session management with resumption
- [x] Interruption handling and priority management
- [x] Visual validation and feedback
- [x] Modular, event-driven architecture

### In Progress 🚧
- [ ] Multi-modal audio interaction
- [ ] Advanced visual grounding and element detection
- [ ] Performance optimization and bandwidth management
- [ ] Enhanced error recovery and resilience

### Future Enhancements 🔮
- [ ] Voice activity detection integration
- [ ] Custom visual element training
- [ ] Multi-screen support
- [ ] Task planning and workflow automation
- [ ] Integration with external APIs and services

## 🤝 Contributing

Feel free to open issues to discuss improvements or submit pull requests. This project represents a significant advancement in computer control using AI, and community contributions are welcome!

## 📄 License

Apache-2.0 License - see LICENSE file for details.