"""Event bus for inter-component communication."""

import asyncio
import logging
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from enum import Enum


class EventType(Enum):
    """Event types for the system."""
    # Visual events
    SCREEN_CHANGED = "screen_changed"
    VISUAL_ELEMENT_DETECTED = "visual_element_detected"
    VISUAL_INTERRUPTION = "visual_interruption"
    
    # Session events
    SESSION_STARTED = "session_started"
    SESSION_ENDED = "session_ended"
    SESSION_RESUMED = "session_resumed"
    
    # Control events
    ACTION_REQUESTED = "action_requested"
    ACTION_COMPLETED = "action_completed"
    ACTION_FAILED = "action_failed"
    
    # API events
    API_RESPONSE_RECEIVED = "api_response_received"
    API_ERROR = "api_error"
    API_RECONNECTED = "api_reconnected"


@dataclass
class Event:
    """Event data structure."""
    type: EventType
    data: Any
    timestamp: float
    source: str
    correlation_id: Optional[str] = None


class EventBus:
    """Event bus for decoupled communication between components."""
    
    def __init__(self):
        self._subscribers: Dict[EventType, List[Callable]] = {}
        self._logger = logging.getLogger(__name__)
        self._event_queue = asyncio.Queue()
        self._processing = False
    
    def subscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> None:
        """Subscribe to an event type."""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(callback)
        self._logger.debug(f"Subscribed to {event_type.value}")
    
    def unsubscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> None:
        """Unsubscribe from an event type."""
        if event_type in self._subscribers:
            try:
                self._subscribers[event_type].remove(callback)
                self._logger.debug(f"Unsubscribed from {event_type.value}")
            except ValueError:
                pass
    
    async def publish(self, event: Event) -> None:
        """Publish an event."""
        await self._event_queue.put(event)
        self._logger.debug(f"Published event: {event.type.value}")
    
    async def emit(self, event_type: EventType, data: Any, source: str, 
                   correlation_id: Optional[str] = None) -> None:
        """Emit an event with the given data."""
        import time
        event = Event(
            type=event_type,
            data=data,
            timestamp=time.time(),
            source=source,
            correlation_id=correlation_id
        )
        await self.publish(event)
    
    async def start_processing(self) -> None:
        """Start processing events from the queue."""
        self._processing = True
        while self._processing:
            try:
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._process_event(event)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self._logger.error(f"Error processing event: {e}")
    
    def stop_processing(self) -> None:
        """Stop processing events."""
        self._processing = False
    
    async def _process_event(self, event: Event) -> None:
        """Process a single event."""
        if event.type in self._subscribers:
            for callback in self._subscribers[event.type]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(event)
                    else:
                        callback(event)
                except Exception as e:
                    self._logger.error(f"Error in event callback: {e}")


# Global event bus instance
_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """Get the global event bus instance."""
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus
