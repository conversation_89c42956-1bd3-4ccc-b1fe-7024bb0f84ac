"""Response handler for Live API responses."""

import asyncio
import logging
from typing import Any

from ..core.session_manager import <PERSON><PERSON><PERSON><PERSON>
from ..core.event_bus import get_event_bus, EventType
from ..control.tool_manager import Too<PERSON><PERSON>anager
from ..vision.visual_processor import VisualProcessor


class ResponseHandler:
    """Handles responses from the Live API."""
    
    def __init__(self, session_manager: <PERSON><PERSON><PERSON><PERSON>, 
                 tool_manager: <PERSON>lManager,
                 visual_processor: VisualProcessor):
        self.session_manager = session_manager
        self.tool_manager = tool_manager
        self.visual_processor = visual_processor
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
    async def handle_response(self, response: Any) -> None:
        """Handle a response from the Live API."""
        try:
            # Handle text responses
            if response.text:
                await self._handle_text_response(response.text)
            
            # Handle audio responses
            if response.data:
                await self._handle_audio_response(response.data)
            
            # Handle tool calls
            if response.tool_call:
                await self._handle_tool_call(response.tool_call)
            
            # Handle server content
            if response.server_content:
                await self._handle_server_content(response.server_content)
            
            # Handle session resumption updates
            if response.session_resumption_update:
                await self._handle_session_resumption(response.session_resumption_update)
            
            # Handle usage metadata
            if response.usage_metadata:
                await self._handle_usage_metadata(response.usage_metadata)
            
            # Handle go away messages
            if response.go_away:
                await self._handle_go_away(response.go_away)
                
        except Exception as e:
            self.logger.error(f"Error handling response: {e}")
    
    async def _handle_text_response(self, text: str) -> None:
        """Handle text response from the model."""
        self.logger.info(f"Model response: {text[:200]}{'...' if len(text) > 200 else ''}")
        
        # Emit API response event
        await self.event_bus.emit(
            EventType.API_RESPONSE_RECEIVED,
            {
                "type": "text",
                "content": text,
                "length": len(text)
            },
            "response_handler"
        )
        
        # Log the response for debugging
        if self.logger.isEnabledFor(logging.DEBUG):
            self.logger.debug(f"Full text response: {text}")
    
    async def _handle_audio_response(self, audio_data: bytes) -> None:
        """Handle audio response from the model."""
        self.logger.info(f"Received audio response: {len(audio_data)} bytes")
        
        # Emit API response event
        await self.event_bus.emit(
            EventType.API_RESPONSE_RECEIVED,
            {
                "type": "audio",
                "size": len(audio_data)
            },
            "response_handler"
        )
        
        # TODO: Implement audio playback if needed
        # For now, just log that we received audio
    
    async def _handle_tool_call(self, tool_call: Any) -> None:
        """Handle tool call from the model."""
        self.logger.info(f"Received tool call with {len(tool_call.function_calls)} functions")
        
        try:
            # Delegate to tool manager
            await self.tool_manager.handle_tool_call(tool_call)
            
        except Exception as e:
            self.logger.error(f"Error handling tool call: {e}")
            # Continue processing other responses
    
    async def _handle_server_content(self, server_content: Any) -> None:
        """Handle server content messages."""
        try:
            # Handle interruptions
            if hasattr(server_content, 'interrupted') and server_content.interrupted:
                self.logger.info("Generation was interrupted")
                await self.event_bus.emit(
                    EventType.VISUAL_INTERRUPTION,
                    {"type": "generation_interrupted"},
                    "response_handler"
                )
            
            # Handle generation complete
            if hasattr(server_content, 'generation_complete') and server_content.generation_complete:
                self.logger.info("Generation completed")
                await self.event_bus.emit(
                    EventType.API_RESPONSE_RECEIVED,
                    {"type": "generation_complete"},
                    "response_handler"
                )
            
            # Handle turn complete
            if hasattr(server_content, 'turn_complete') and server_content.turn_complete:
                self.logger.debug("Turn completed")
            
            # Handle model turn
            if hasattr(server_content, 'model_turn') and server_content.model_turn:
                await self._handle_model_turn(server_content.model_turn)
            
            # Handle input transcription
            if hasattr(server_content, 'input_transcription') and server_content.input_transcription:
                self.logger.info(f"Input transcription: {server_content.input_transcription.text}")
            
            # Handle output transcription
            if hasattr(server_content, 'output_transcription') and server_content.output_transcription:
                self.logger.info(f"Output transcription: {server_content.output_transcription.text}")
                
        except Exception as e:
            self.logger.error(f"Error handling server content: {e}")
    
    async def _handle_model_turn(self, model_turn: Any) -> None:
        """Handle model turn content."""
        try:
            if hasattr(model_turn, 'parts') and model_turn.parts:
                for part in model_turn.parts:
                    # Handle executable code
                    if hasattr(part, 'executable_code') and part.executable_code:
                        self.logger.info(f"Model generated code: {part.executable_code.code[:100]}...")
                    
                    # Handle code execution result
                    if hasattr(part, 'code_execution_result') and part.code_execution_result:
                        self.logger.info(f"Code execution result: {part.code_execution_result.output[:100]}...")
                        
        except Exception as e:
            self.logger.error(f"Error handling model turn: {e}")
    
    async def _handle_session_resumption(self, resumption_update: Any) -> None:
        """Handle session resumption update."""
        try:
            if hasattr(resumption_update, 'resumable') and resumption_update.resumable:
                if hasattr(resumption_update, 'new_handle') and resumption_update.new_handle:
                    # Update session handle for future resumption
                    self.session_manager.session_handle = resumption_update.new_handle
                    self.logger.info("Updated session resumption handle")
                    
        except Exception as e:
            self.logger.error(f"Error handling session resumption: {e}")
    
    async def _handle_usage_metadata(self, usage_metadata: Any) -> None:
        """Handle usage metadata."""
        try:
            if hasattr(usage_metadata, 'total_token_count'):
                self.logger.debug(f"Total tokens used: {usage_metadata.total_token_count}")
                
                # Log detailed token breakdown if available
                if hasattr(usage_metadata, 'response_tokens_details'):
                    for detail in usage_metadata.response_tokens_details:
                        if hasattr(detail, 'modality') and hasattr(detail, 'token_count'):
                            self.logger.debug(f"{detail.modality}: {detail.token_count} tokens")
                            
        except Exception as e:
            self.logger.error(f"Error handling usage metadata: {e}")
    
    async def _handle_go_away(self, go_away: Any) -> None:
        """Handle go away message."""
        try:
            if hasattr(go_away, 'time_left'):
                self.logger.warning(f"Server will disconnect in {go_away.time_left}")
                
                # Emit event for potential reconnection handling
                await self.event_bus.emit(
                    EventType.API_ERROR,
                    {
                        "type": "go_away",
                        "time_left": str(go_away.time_left)
                    },
                    "response_handler"
                )
                
        except Exception as e:
            self.logger.error(f"Error handling go away message: {e}")
