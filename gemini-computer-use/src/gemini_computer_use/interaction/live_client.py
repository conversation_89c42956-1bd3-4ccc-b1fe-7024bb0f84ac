"""Live API client for real-time interaction."""

import asyncio
import logging
from typing import Optional, Dict, Any

from ..core.session_manager import <PERSON><PERSON>anager
from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType
from ..vision.video_stream import VideoStream
from ..vision.visual_processor import VisualProcessor
from ..control.tool_manager import ToolManager
from .response_handler import <PERSON>Handler
from .interruption_manager import InterruptionManager


class LiveClient:
    """Main client for Live API interaction with continuous visual processing."""
    
    def __init__(self):
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.session_manager = SessionManager()
        self.visual_processor: Optional[VisualProcessor] = None
        self.video_stream: Optional[VideoStream] = None
        self.tool_manager: Optional[ToolManager] = None
        self.response_handler: Optional[ResponseHandler] = None
        self.interruption_manager: Optional[InterruptionManager] = None
        
        # State
        self.is_running = False
        self.current_goal: Optional[str] = None
        self.interaction_task: Optional[asyncio.Task] = None
        
    async def initialize(self) -> None:
        """Initialize all components."""
        try:
            self.logger.info("Initializing Live API client")
            
            # Initialize session manager
            await self.session_manager.initialize()
            
            # Initialize visual processor
            self.visual_processor = VisualProcessor(self.session_manager)
            
            # Initialize video stream
            self.video_stream = VideoStream(self.session_manager)
            
            # Initialize tool manager (requires computer controller)
            from ..control.computer_controller import ComputerController
            computer_controller = ComputerController(self.visual_processor)
            self.tool_manager = ToolManager(
                self.session_manager, 
                computer_controller, 
                self.visual_processor
            )
            
            # Initialize response handler
            self.response_handler = ResponseHandler(
                self.session_manager,
                self.tool_manager,
                self.visual_processor
            )
            
            # Initialize interruption manager
            self.interruption_manager = InterruptionManager(
                self.session_manager,
                self.visual_processor
            )
            
            self.logger.info("Live API client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Live API client: {e}")
            raise
    
    async def start(self, goal: str) -> None:
        """Start the Live API client with a goal."""
        if self.is_running:
            await self.stop()
        
        try:
            self.current_goal = goal
            self.is_running = True
            
            self.logger.info(f"Starting Live API client with goal: {goal}")
            
            # Connect to Live API
            await self.session_manager.connect()
            
            # Configure session with tools
            await self._configure_session()
            
            # Start visual processing
            await self.visual_processor.start_processing()
            
            # Start video streaming
            await self.video_stream.start_streaming()
            
            # Start interruption management
            await self.interruption_manager.start()
            
            # Start event bus processing
            asyncio.create_task(self.event_bus.start_processing())
            
            # Send initial goal
            await self._send_initial_goal(goal)
            
            # Start main interaction loop
            self.interaction_task = asyncio.create_task(self._interaction_loop())
            
            self.logger.info("Live API client started successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to start Live API client: {e}")
            await self.stop()
            raise
    
    async def stop(self) -> None:
        """Stop the Live API client."""
        if not self.is_running:
            return
        
        self.logger.info("Stopping Live API client")
        self.is_running = False
        
        try:
            # Cancel interaction task
            if self.interaction_task:
                self.interaction_task.cancel()
                try:
                    await self.interaction_task
                except asyncio.CancelledError:
                    pass
            
            # Stop components
            if self.interruption_manager:
                await self.interruption_manager.stop()
            
            if self.video_stream:
                await self.video_stream.stop_streaming()
            
            if self.visual_processor:
                await self.visual_processor.stop_processing()
            
            # Stop event bus
            self.event_bus.stop_processing()
            
            # Disconnect from Live API
            await self.session_manager.disconnect()
            
            self.logger.info("Live API client stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping Live API client: {e}")
    
    async def _configure_session(self) -> None:
        """Configure the Live API session with tools."""
        # This would be done during session creation in session_manager
        # Tools are configured in the LiveConnectConfig
        pass
    
    async def _send_initial_goal(self, goal: str) -> None:
        """Send the initial goal to the Live API."""
        try:
            initial_prompt = f"""
            I need you to help me accomplish the following goal using the computer:
            
            Goal: {goal}
            
            You have access to a live video feed of the computer screen and can see everything in real-time.
            You also have tools to interact with the computer including clicking, typing, and analyzing specific regions.
            
            Please:
            1. Analyze the current screen state
            2. Plan the steps needed to accomplish the goal
            3. Execute the plan step by step
            4. Provide clear explanations of what you're doing
            5. Handle any errors or unexpected situations
            
            Start by analyzing what you can see on the screen right now.
            """
            
            await self.session_manager.send_client_content(
                turns={
                    "role": "user",
                    "parts": [{"text": initial_prompt}]
                },
                turn_complete=True
            )
            
        except Exception as e:
            self.logger.error(f"Error sending initial goal: {e}")
            raise
    
    async def _interaction_loop(self) -> None:
        """Main interaction loop for processing Live API responses."""
        try:
            while self.is_running:
                try:
                    # Process responses from Live API
                    async for response in self.session_manager.receive():
                        if not self.is_running:
                            break
                        
                        # Handle response
                        await self.response_handler.handle_response(response)
                        
                        # Check for session end conditions
                        if response.server_content and response.server_content.generation_complete:
                            # Generation complete, but continue listening for more
                            continue
                        
                        if response.go_away:
                            self.logger.warning("Received go_away message from server")
                            # Attempt reconnection
                            await self.session_manager.reconnect()
                            break
                
                except Exception as e:
                    self.logger.error(f"Error in interaction loop: {e}")
                    
                    # Attempt to reconnect
                    if self.is_running:
                        await asyncio.sleep(1.0)
                        try:
                            await self.session_manager.reconnect()
                        except Exception as reconnect_error:
                            self.logger.error(f"Failed to reconnect: {reconnect_error}")
                            break
        
        except asyncio.CancelledError:
            self.logger.info("Interaction loop cancelled")
        except Exception as e:
            self.logger.error(f"Fatal error in interaction loop: {e}")
        finally:
            self.is_running = False
    
    async def send_message(self, message: str) -> None:
        """Send a message to the Live API."""
        if not self.is_running:
            raise RuntimeError("Client is not running")
        
        try:
            await self.session_manager.send_client_content(
                turns={
                    "role": "user",
                    "parts": [{"text": message}]
                },
                turn_complete=True
            )
            
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            raise
    
    async def interrupt_current_action(self) -> None:
        """Interrupt the current action."""
        if self.interruption_manager:
            await self.interruption_manager.trigger_interruption("user_request")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current client status."""
        status = {
            "is_running": self.is_running,
            "current_goal": self.current_goal,
            "session_connected": self.session_manager.is_connected if self.session_manager else False
        }
        
        if self.video_stream:
            status["video_streaming"] = self.video_stream.get_streaming_stats()
        
        if self.visual_processor:
            current_context = self.visual_processor.get_current_context()
            status["visual_context"] = {
                "has_context": current_context is not None,
                "elements_count": len(current_context.elements) if current_context else 0,
                "last_update": current_context.timestamp if current_context else None
            }
        
        return status
