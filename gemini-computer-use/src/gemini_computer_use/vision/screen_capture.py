"""Real-time screen capture with optimization."""

import asyncio
import logging
import time
from typing import Optional, <PERSON>ple
import numpy as np
import cv2
import pyautogui
from PIL import Image

from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType


class ScreenCapture:
    """Handles continuous screen capture with adaptive frame rate and change detection."""
    
    def __init__(self):
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        self.is_capturing = False
        self.current_fps = self.config.visual.fps
        self.frame_buffer = asyncio.Queue(maxsize=self.config.visual.frame_buffer_size)
        self.last_frame: Optional[np.ndarray] = None
        self.last_capture_time = 0
        
        # Change detection
        self.change_threshold = self.config.visual.change_detection_threshold
        self.frames_without_change = 0
        self.max_frames_without_change = 30  # Reduce FPS after 30 frames without change
        
    async def start_capture(self) -> None:
        """Start continuous screen capture."""
        if self.is_capturing:
            return
        
        self.is_capturing = True
        self.logger.info(f"Starting screen capture at {self.current_fps} FPS")
        
        # Start capture loop
        asyncio.create_task(self._capture_loop())
    
    async def stop_capture(self) -> None:
        """Stop screen capture."""
        self.is_capturing = False
        self.logger.info("Stopped screen capture")
    
    async def get_frame(self) -> Optional[bytes]:
        """Get the next frame from the buffer."""
        try:
            frame_data = await asyncio.wait_for(self.frame_buffer.get(), timeout=1.0)
            return frame_data
        except asyncio.TimeoutError:
            return None
    
    async def _capture_loop(self) -> None:
        """Main capture loop."""
        while self.is_capturing:
            try:
                start_time = time.time()
                
                # Capture frame
                frame = await self._capture_frame()
                if frame is not None:
                    # Check for changes
                    has_changed = self._detect_change(frame)
                    
                    if has_changed:
                        self.frames_without_change = 0
                        # Encode and buffer frame
                        encoded_frame = self._encode_frame(frame)
                        
                        # Add to buffer (non-blocking)
                        try:
                            self.frame_buffer.put_nowait(encoded_frame)
                        except asyncio.QueueFull:
                            # Remove oldest frame and add new one
                            try:
                                self.frame_buffer.get_nowait()
                                self.frame_buffer.put_nowait(encoded_frame)
                            except asyncio.QueueEmpty:
                                pass
                        
                        # Emit screen change event
                        await self.event_bus.emit(
                            EventType.SCREEN_CHANGED,
                            {"frame_size": len(encoded_frame), "timestamp": start_time},
                            "screen_capture"
                        )
                    else:
                        self.frames_without_change += 1
                
                # Adaptive FPS adjustment
                if self.config.visual.enable_adaptive_fps:
                    self._adjust_fps()
                
                # Calculate sleep time to maintain FPS
                elapsed = time.time() - start_time
                target_frame_time = 1.0 / self.current_fps
                sleep_time = max(0, target_frame_time - elapsed)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except Exception as e:
                self.logger.error(f"Error in capture loop: {e}")
                await asyncio.sleep(0.1)  # Brief pause before retrying
    
    async def _capture_frame(self) -> Optional[np.ndarray]:
        """Capture a single frame."""
        try:
            # Use pyautogui for cross-platform compatibility
            screenshot = pyautogui.screenshot()
            
            # Resize if needed
            target_resolution = self.config.visual.capture_resolution
            if screenshot.size != target_resolution:
                screenshot = screenshot.resize(target_resolution, Image.Resampling.LANCZOS)
            
            # Convert to numpy array
            frame = np.array(screenshot)
            frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"Error capturing frame: {e}")
            return None
    
    def _detect_change(self, current_frame: np.ndarray) -> bool:
        """Detect if the frame has changed significantly."""
        if self.last_frame is None:
            self.last_frame = current_frame.copy()
            return True
        
        # Calculate frame difference
        diff = cv2.absdiff(self.last_frame, current_frame)
        gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
        
        # Calculate percentage of changed pixels
        total_pixels = gray_diff.shape[0] * gray_diff.shape[1]
        changed_pixels = np.count_nonzero(gray_diff > 30)  # Threshold for significant change
        change_percentage = changed_pixels / total_pixels
        
        has_changed = change_percentage > self.change_threshold
        
        if has_changed:
            self.last_frame = current_frame.copy()
        
        return has_changed
    
    def _adjust_fps(self) -> None:
        """Adjust FPS based on screen activity."""
        if self.frames_without_change > self.max_frames_without_change:
            # Reduce FPS when no changes detected
            self.current_fps = max(self.config.visual.min_fps, self.current_fps * 0.8)
        else:
            # Increase FPS when changes detected
            self.current_fps = min(self.config.visual.max_fps, self.config.visual.fps)
    
    def _encode_frame(self, frame: np.ndarray) -> bytes:
        """Encode frame for transmission."""
        # Encode as JPEG with specified quality
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), self.config.visual.compression_quality]
        _, encoded_img = cv2.imencode('.jpg', frame, encode_param)
        return encoded_img.tobytes()
    
    def get_current_fps(self) -> float:
        """Get current capture FPS."""
        return self.current_fps
    
    def get_buffer_size(self) -> int:
        """Get current buffer size."""
        return self.frame_buffer.qsize()
