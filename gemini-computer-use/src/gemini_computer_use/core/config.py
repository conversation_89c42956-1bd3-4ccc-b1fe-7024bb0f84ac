"""Configuration management for Gemini Computer Use Live API."""

import os
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any
from enum import Enum


class MediaResolution(Enum):
    """Media resolution options for Live API."""
    LOW = "MEDIA_RESOLUTION_LOW"
    MEDIUM = "MEDIA_RESOLUTION_MEDIUM"
    HIGH = "MEDIA_RESOLUTION_HIGH"


class ResponseModality(Enum):
    """Response modality options."""
    TEXT = "TEXT"
    AUDIO = "AUDIO"


@dataclass
class LiveAPIConfig:
    """Configuration for Gemini Live API."""
    model: str = "gemini-2.0-flash-live-001"
    api_key: Optional[str] = None
    response_modalities: List[ResponseModality] = field(default_factory=lambda: [ResponseModality.TEXT])
    media_resolution: MediaResolution = MediaResolution.MEDIUM
    enable_context_compression: bool = True
    enable_session_resumption: bool = True
    max_session_duration_minutes: int = 60
    
    def __post_init__(self):
        """Initialize API key from environment if not provided."""
        if self.api_key is None:
            self.api_key = os.getenv("GOOGLE_API_KEY")
            if not self.api_key:
                raise ValueError("GOOGLE_API_KEY environment variable must be set")


@dataclass
class VisualConfig:
    """Configuration for visual processing."""
    fps: int = 30
    capture_resolution: tuple = (1920, 1080)
    compression_quality: int = 85
    frame_buffer_size: int = 10
    change_detection_threshold: float = 0.1
    enable_adaptive_fps: bool = True
    min_fps: int = 10
    max_fps: int = 60


@dataclass
class ControlConfig:
    """Configuration for computer control."""
    click_delay: float = 0.1
    type_delay: float = 0.05
    screenshot_delay: float = 0.5
    max_retries: int = 3
    confidence_threshold: float = 0.8
    enable_visual_validation: bool = True


@dataclass
class Config:
    """Main configuration class."""
    live_api: LiveAPIConfig = field(default_factory=LiveAPIConfig)
    visual: VisualConfig = field(default_factory=VisualConfig)
    control: ControlConfig = field(default_factory=ControlConfig)
    debug: bool = False
    log_level: str = "INFO"
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "Config":
        """Create config from dictionary."""
        return cls(
            live_api=LiveAPIConfig(**config_dict.get("live_api", {})),
            visual=VisualConfig(**config_dict.get("visual", {})),
            control=ControlConfig(**config_dict.get("control", {})),
            debug=config_dict.get("debug", False),
            log_level=config_dict.get("log_level", "INFO")
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            "live_api": self.live_api.__dict__,
            "visual": self.visual.__dict__,
            "control": self.control.__dict__,
            "debug": self.debug,
            "log_level": self.log_level
        }


# Global config instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
    return _config


def set_config(config: Config) -> None:
    """Set the global configuration instance."""
    global _config
    _config = config
