[project]
name = "gemini-computer-use"
version = "0.2.0"
description = "Advanced computer control using Gemini 2.0 Live API with real-time visual processing"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "google-genai>=0.2.0",
    "pyautogui>=0.9.54",
    "opencv-python>=4.8.0",
    "numpy>=1.24.0",
    "pillow>=10.0.0",
    "asyncio-mqtt>=0.13.0",
    "websockets>=12.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0"
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "Apache-2.0"}


[tool.pdm]
distribution = false

[tool.pdm.scripts]
start = {call = "src.gemini_computer_use.main:main"}
