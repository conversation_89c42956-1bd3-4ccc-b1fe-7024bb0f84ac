"""
Refactored main entry point for Gemini Computer Use with Live API.

This module provides the main entry point for the enhanced computer control system
that uses Gemini 2.0 Live API with real-time visual processing and continuous
screen monitoring.
"""

import asyncio
import logging
import sys
import signal
from typing import Optional

from .core.config import Config, get_config, set_config
from .interaction.live_client import LiveClient


# Global client instance for signal handling
_client: Optional[LiveClient] = None


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration."""
    level = logging.DEBUG if debug else logging.INFO

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('gemini_computer_use.log')
        ]
    )

    # Reduce noise from some libraries
    logging.getLogger('google').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


async def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    global _client

    print(f"\nReceived signal {signum}, shutting down gracefully...")

    if _client:
        await _client.stop()

    sys.exit(0)


async def main_async():
    """Main async function."""
    global _client

    # Parse command line arguments
    if len(sys.argv) < 2:
        print("Usage: python -m gemini_computer_use.main <goal> [--debug]")
        print("Example: python -m gemini_computer_use.main 'search for cat images with google'")
        sys.exit(1)

    goal = sys.argv[1]
    debug = "--debug" in sys.argv

    # Setup logging
    setup_logging(debug)
    logger = logging.getLogger(__name__)

    # Setup configuration
    config = Config()
    config.debug = debug
    if debug:
        config.log_level = "DEBUG"
    set_config(config)

    logger.info(f"Starting Gemini Computer Use with goal: {goal}")
    logger.info(f"Debug mode: {debug}")

    try:
        # Initialize Live API client
        _client = LiveClient()
        await _client.initialize()

        # Setup signal handlers
        signal.signal(signal.SIGINT, lambda s, f: asyncio.create_task(signal_handler(s, f)))
        signal.signal(signal.SIGTERM, lambda s, f: asyncio.create_task(signal_handler(s, f)))

        # Start the client with the goal
        await _client.start(goal)

        # Keep running until interrupted
        logger.info("Live API client is running. Press Ctrl+C to stop.")

        while _client.is_running:
            await asyncio.sleep(1.0)

            # Print status periodically
            if debug:
                status = _client.get_status()
                logger.debug(f"Client status: {status}")

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        raise
    finally:
        if _client:
            await _client.stop()
        logger.info("Gemini Computer Use stopped")


def main():
    """Main entry point."""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\nShutdown complete.")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


