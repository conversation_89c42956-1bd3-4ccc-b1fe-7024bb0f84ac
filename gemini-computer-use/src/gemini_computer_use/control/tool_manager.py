"""Tool manager for Live API function calling integration."""

import asyncio
import logging
import json
from typing import Dict, List, Any, Callable, Optional
from google.genai import types

from ..core.session_manager import SessionManager
from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType
from .computer_controller import ComputerController
from ..vision.visual_processor import VisualProcessor


class ToolManager:
    """Manages Live API tools and function calling."""
    
    def __init__(self, session_manager: SessionManager, 
                 computer_controller: ComputerController,
                 visual_processor: VisualProcessor):
        self.session_manager = session_manager
        self.computer_controller = computer_controller
        self.visual_processor = visual_processor
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        # Tool registry
        self.tools: Dict[str, Callable] = {}
        self.tool_definitions: List[Dict[str, Any]] = []
        
        # Initialize tools
        self._register_tools()
    
    def _register_tools(self) -> None:
        """Register all available tools."""
        # Computer control tools
        self._register_computer_tools()
        
        # Visual analysis tools
        self._register_visual_tools()
        
        # System tools
        self._register_system_tools()
        
        self.logger.info(f"Registered {len(self.tools)} tools")
    
    def _register_computer_tools(self) -> None:
        """Register computer control tools."""
        
        # Click tool
        self.tools["click_visual_element"] = self._click_visual_element
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "click_visual_element",
                "description": "Click on a visual element identified in the live screen feed",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "element_description": {
                            "type": "string",
                            "description": "Description of the element to click (e.g., 'submit button', 'search field')"
                        },
                        "confidence_threshold": {
                            "type": "number",
                            "description": "Minimum confidence threshold (0.0-1.0)",
                            "default": 0.8
                        },
                        "wait_for_change": {
                            "type": "boolean",
                            "description": "Whether to wait for visual change after click",
                            "default": True
                        }
                    },
                    "required": ["element_description"]
                }
            }]
        })
        
        # Type text tool
        self.tools["type_text"] = self._type_text
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "type_text",
                "description": "Type text at the current cursor position",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "text": {
                            "type": "string",
                            "description": "Text to type"
                        },
                        "delay": {
                            "type": "number",
                            "description": "Delay between characters in seconds",
                            "default": 0.05
                        }
                    },
                    "required": ["text"]
                }
            }]
        })
        
        # Press keys tool
        self.tools["press_keys"] = self._press_keys
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "press_keys",
                "description": "Press keyboard keys or key combinations",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "keys": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of keys to press simultaneously"
                        },
                        "hold_duration": {
                            "type": "number",
                            "description": "Duration to hold keys in seconds",
                            "default": 0.1
                        }
                    },
                    "required": ["keys"]
                }
            }]
        })
        
        # Scroll tool
        self.tools["scroll"] = self._scroll
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "scroll",
                "description": "Scroll the screen in a specified direction",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "direction": {
                            "type": "string",
                            "enum": ["up", "down"],
                            "description": "Direction to scroll"
                        },
                        "amount": {
                            "type": "integer",
                            "description": "Amount to scroll",
                            "default": 3
                        }
                    },
                    "required": ["direction"]
                }
            }]
        })
    
    def _register_visual_tools(self) -> None:
        """Register visual analysis tools."""
        
        # Analyze region tool
        self.tools["analyze_screen_region"] = self._analyze_screen_region
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "analyze_screen_region",
                "description": "Analyze a specific region of the screen in detail",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "x": {"type": "integer", "description": "X coordinate of region"},
                        "y": {"type": "integer", "description": "Y coordinate of region"},
                        "width": {"type": "integer", "description": "Width of region"},
                        "height": {"type": "integer", "description": "Height of region"},
                        "analysis_type": {
                            "type": "string",
                            "enum": ["general", "text", "elements", "state"],
                            "description": "Type of analysis to perform",
                            "default": "general"
                        }
                    },
                    "required": ["x", "y", "width", "height"]
                }
            }]
        })
        
        # Monitor changes tool
        self.tools["monitor_visual_change"] = self._monitor_visual_change
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "monitor_visual_change",
                "description": "Monitor for specific visual changes in the live feed",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "change_description": {
                            "type": "string",
                            "description": "Description of the change to monitor for"
                        },
                        "timeout_seconds": {
                            "type": "number",
                            "description": "Maximum time to wait for change",
                            "default": 10.0
                        },
                        "action_on_change": {
                            "type": "string",
                            "description": "Action to take when change is detected",
                            "default": "notify"
                        }
                    },
                    "required": ["change_description"]
                }
            }]
        })
    
    def _register_system_tools(self) -> None:
        """Register system tools."""
        
        # Spotlight search tool (macOS)
        self.tools["open_spotlight"] = self._open_spotlight
        self.tool_definitions.append({
            "function_declarations": [{
                "name": "open_spotlight",
                "description": "Open macOS Spotlight search with a search term",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "search_term": {
                            "type": "string",
                            "description": "Term to search for in Spotlight"
                        }
                    },
                    "required": ["search_term"]
                }
            }]
        })
    
    async def handle_tool_call(self, tool_call) -> None:
        """Handle a tool call from the Live API."""
        try:
            function_responses = []
            
            for function_call in tool_call.function_calls:
                self.logger.info(f"Executing tool: {function_call.name}")
                
                # Get tool function
                if function_call.name not in self.tools:
                    error_msg = f"Unknown tool: {function_call.name}"
                    self.logger.error(error_msg)
                    
                    function_responses.append(types.FunctionResponse(
                        id=function_call.id,
                        name=function_call.name,
                        response={"error": error_msg}
                    ))
                    continue
                
                # Execute tool
                try:
                    tool_function = self.tools[function_call.name]
                    
                    # Parse arguments
                    args = json.loads(function_call.args) if function_call.args else {}
                    
                    # Execute tool function
                    result = await tool_function(**args)
                    
                    # Create response
                    function_responses.append(types.FunctionResponse(
                        id=function_call.id,
                        name=function_call.name,
                        response={"result": result, "success": True}
                    ))
                    
                except Exception as e:
                    error_msg = f"Error executing {function_call.name}: {str(e)}"
                    self.logger.error(error_msg)
                    
                    function_responses.append(types.FunctionResponse(
                        id=function_call.id,
                        name=function_call.name,
                        response={"error": error_msg, "success": False}
                    ))
            
            # Send responses back to Live API
            await self.session_manager.session.send_tool_response(
                function_responses=function_responses
            )
            
        except Exception as e:
            self.logger.error(f"Error handling tool call: {e}")
    
    # Tool implementation methods
    async def _click_visual_element(self, element_description: str, 
                                   confidence_threshold: float = 0.8,
                                   wait_for_change: bool = True) -> Dict[str, Any]:
        """Click on a visual element."""
        success = await self.computer_controller.click_element(
            element_description, confidence_threshold
        )
        return {
            "clicked": success,
            "element": element_description,
            "confidence_threshold": confidence_threshold
        }
    
    async def _type_text(self, text: str, delay: float = 0.05) -> Dict[str, Any]:
        """Type text."""
        success = await self.computer_controller.type_text(text, delay)
        return {
            "typed": success,
            "text": text,
            "length": len(text)
        }
    
    async def _press_keys(self, keys: List[str], hold_duration: float = 0.1) -> Dict[str, Any]:
        """Press keys."""
        success = await self.computer_controller.press_keys(keys, hold_duration)
        return {
            "pressed": success,
            "keys": keys
        }
    
    async def _scroll(self, direction: str, amount: int = 3) -> Dict[str, Any]:
        """Scroll screen."""
        success = await self.computer_controller.scroll(direction, amount)
        return {
            "scrolled": success,
            "direction": direction,
            "amount": amount
        }
    
    async def _analyze_screen_region(self, x: int, y: int, width: int, height: int,
                                   analysis_type: str = "general") -> Dict[str, Any]:
        """Analyze screen region."""
        context = await self.visual_processor.analyze_specific_region(x, y, width, height)
        return {
            "analyzed": True,
            "region": {"x": x, "y": y, "width": width, "height": height},
            "analysis_type": analysis_type,
            "context": context.screen_state if context else "No analysis available"
        }
    
    async def _monitor_visual_change(self, change_description: str,
                                   timeout_seconds: float = 10.0,
                                   action_on_change: str = "notify") -> Dict[str, Any]:
        """Monitor for visual changes."""
        # This is a simplified implementation
        # In practice, you'd want more sophisticated change monitoring
        await asyncio.sleep(min(timeout_seconds, 1.0))  # Wait briefly
        
        return {
            "monitored": True,
            "change_description": change_description,
            "timeout": timeout_seconds,
            "detected": False  # Simplified - would need actual change detection
        }
    
    async def _open_spotlight(self, search_term: str) -> Dict[str, Any]:
        """Open Spotlight search."""
        success = await self.computer_controller.open_spotlight(search_term)
        return {
            "opened": success,
            "search_term": search_term
        }
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """Get all tool definitions for Live API configuration."""
        return self.tool_definitions.copy()
