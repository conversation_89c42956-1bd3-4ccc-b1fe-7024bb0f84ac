"""Visual processing and context management."""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from ..core.session_manager import Session<PERSON>anager
from ..core.config import get_config
from ..core.event_bus import get_event_bus, EventType


@dataclass
class VisualElement:
    """Represents a visual element detected on screen."""
    type: str
    description: str
    bounds: Dict[str, int]  # x, y, width, height
    confidence: float
    timestamp: float
    properties: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VisualContext:
    """Current visual context of the screen."""
    timestamp: float
    elements: List[VisualElement] = field(default_factory=list)
    screen_state: str = ""
    active_window: str = ""
    changes_detected: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class VisualProcessor:
    """Processes visual information and maintains context."""
    
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        self.current_context: Optional[VisualContext] = None
        self.context_history: List[VisualContext] = []
        self.max_history_size = 50
        
        self.is_processing = False
        self.processing_task: Optional[asyncio.Task] = None
        
        # Visual analysis prompts
        self.analysis_prompts = {
            "general": "Analyze the current screen and describe what you see. Identify any interactive elements, text, images, and their locations.",
            "elements": "Identify all clickable elements, buttons, links, and form fields visible on the screen. Provide their approximate locations.",
            "changes": "Compare this screen with the previous state and describe any changes that have occurred.",
            "state": "Describe the current state of the application or system shown on screen."
        }
    
    async def start_processing(self) -> None:
        """Start visual processing."""
        if self.is_processing:
            return
        
        self.is_processing = True
        self.logger.info("Starting visual processing")
        
        # Subscribe to screen change events
        self.event_bus.subscribe(EventType.SCREEN_CHANGED, self._on_screen_changed)
        
        # Start processing task
        self.processing_task = asyncio.create_task(self._processing_loop())
    
    async def stop_processing(self) -> None:
        """Stop visual processing."""
        self.is_processing = False
        
        # Unsubscribe from events
        self.event_bus.unsubscribe(EventType.SCREEN_CHANGED, self._on_screen_changed)
        
        # Cancel processing task
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Stopped visual processing")
    
    async def _processing_loop(self) -> None:
        """Main processing loop for visual analysis."""
        while self.is_processing:
            try:
                # Periodic comprehensive analysis
                await self._perform_comprehensive_analysis()
                
                # Wait before next analysis
                await asyncio.sleep(5.0)  # Analyze every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in visual processing loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _on_screen_changed(self, event) -> None:
        """Handle screen change events."""
        try:
            # Trigger quick analysis for screen changes
            await self._perform_quick_analysis()
            
        except Exception as e:
            self.logger.error(f"Error handling screen change: {e}")
    
    async def _perform_comprehensive_analysis(self) -> None:
        """Perform comprehensive visual analysis."""
        try:
            # Request comprehensive analysis
            analysis_prompt = f"""
            {self.analysis_prompts['general']}
            
            Please provide a structured analysis including:
            1. Overall screen state and active application
            2. List of interactive elements with approximate locations
            3. Any text content visible
            4. Current task or workflow context
            5. Any errors, notifications, or important messages
            
            Format your response as structured data that can be parsed.
            """
            
            await self.session_manager.send_client_content(
                turns={
                    "role": "user",
                    "parts": [{"text": analysis_prompt}]
                },
                turn_complete=True
            )
            
            # Process response
            await self._process_analysis_response("comprehensive")
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis: {e}")
    
    async def _perform_quick_analysis(self) -> None:
        """Perform quick analysis for screen changes."""
        try:
            analysis_prompt = f"""
            {self.analysis_prompts['changes']}
            
            Focus on:
            1. What changed on the screen?
            2. Are there any new interactive elements?
            3. Any errors or notifications that appeared?
            4. Is the system waiting for user input?
            
            Provide a brief, focused response.
            """
            
            await self.session_manager.send_client_content(
                turns={
                    "role": "user",
                    "parts": [{"text": analysis_prompt}]
                },
                turn_complete=True
            )
            
            # Process response
            await self._process_analysis_response("quick")
            
        except Exception as e:
            self.logger.error(f"Error in quick analysis: {e}")
    
    async def _process_analysis_response(self, analysis_type: str) -> None:
        """Process analysis response from Live API."""
        try:
            async for response in self.session_manager.receive():
                if response.text:
                    # Parse and update visual context
                    context = self._parse_visual_response(response.text, analysis_type)
                    await self._update_context(context)
                    break
                
                if response.server_content and response.server_content.turn_complete:
                    break
                    
        except Exception as e:
            self.logger.error(f"Error processing analysis response: {e}")
    
    def _parse_visual_response(self, response_text: str, analysis_type: str) -> VisualContext:
        """Parse visual analysis response into structured context."""
        # This is a simplified parser - in practice, you'd want more sophisticated parsing
        context = VisualContext(
            timestamp=time.time(),
            screen_state=response_text[:200],  # First 200 chars as state summary
            metadata={"analysis_type": analysis_type}
        )
        
        # Extract elements (simplified - would need more sophisticated parsing)
        if "button" in response_text.lower():
            context.elements.append(VisualElement(
                type="button",
                description="Button detected in analysis",
                bounds={"x": 0, "y": 0, "width": 100, "height": 30},
                confidence=0.8,
                timestamp=time.time()
            ))
        
        return context
    
    async def _update_context(self, new_context: VisualContext) -> None:
        """Update the current visual context."""
        # Store previous context in history
        if self.current_context:
            self.context_history.append(self.current_context)
            
            # Limit history size
            if len(self.context_history) > self.max_history_size:
                self.context_history.pop(0)
        
        # Update current context
        self.current_context = new_context
        
        # Emit context update event
        await self.event_bus.emit(
            EventType.VISUAL_ELEMENT_DETECTED,
            {
                "context": new_context,
                "elements_count": len(new_context.elements)
            },
            "visual_processor"
        )
        
        self.logger.debug(f"Updated visual context with {len(new_context.elements)} elements")
    
    async def analyze_specific_region(self, x: int, y: int, width: int, height: int) -> VisualContext:
        """Analyze a specific region of the screen."""
        try:
            region_prompt = f"""
            Analyze the screen region at coordinates x={x}, y={y}, width={width}, height={height}.
            
            Provide detailed information about:
            1. What is visible in this specific region
            2. Any interactive elements in this area
            3. Text content in this region
            4. The purpose or function of this area
            """
            
            await self.session_manager.send_client_content(
                turns={
                    "role": "user",
                    "parts": [{"text": region_prompt}]
                },
                turn_complete=True
            )
            
            # Get response
            async for response in self.session_manager.receive():
                if response.text:
                    return self._parse_visual_response(response.text, "region")
                
                if response.server_content and response.server_content.turn_complete:
                    break
            
            # Return empty context if no response
            return VisualContext(timestamp=time.time())
            
        except Exception as e:
            self.logger.error(f"Error analyzing region: {e}")
            return VisualContext(timestamp=time.time())
    
    def get_current_context(self) -> Optional[VisualContext]:
        """Get the current visual context."""
        return self.current_context
    
    def get_context_history(self) -> List[VisualContext]:
        """Get the visual context history."""
        return self.context_history.copy()
    
    async def find_element(self, description: str) -> Optional[VisualElement]:
        """Find an element by description."""
        if not self.current_context:
            return None
        
        # Simple search through current elements
        for element in self.current_context.elements:
            if description.lower() in element.description.lower():
                return element
        
        return None
