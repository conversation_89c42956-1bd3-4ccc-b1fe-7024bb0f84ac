"""Session management for Gemini Live API connections."""

import asyncio
import logging
from typing import Optional, Dict, Any
from google import genai
from google.genai import types

from .config import get_config
from .event_bus import get_event_bus, EventType


class SessionManager:
    """Manages Gemini Live API sessions with resumption and error handling."""

    def __init__(self):
        self.config = get_config()
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)

        self.client: Optional[genai.Client] = None
        self.session: Optional[Any] = None
        self.session_handle: Optional[str] = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5

    async def initialize(self) -> None:
        """Initialize the Gemini client."""
        try:
            self.client = genai.Client(api_key=self.config.live_api.api_key)
            self.logger.info("Gemini client initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize Gemini client: {e}")
            raise

    async def connect(self) -> None:
        """Establish connection to Live API."""
        if not self.client:
            await self.initialize()

        try:
            config = self._create_session_config()

            self.session = await self.client.aio.live.connect(
                model=self.config.live_api.model,
                config=config
            )

            self.is_connected = True
            self.reconnect_attempts = 0

            await self.event_bus.emit(
                EventType.SESSION_STARTED,
                {"session_id": id(self.session)},
                "session_manager"
            )

            self.logger.info("Connected to Gemini Live API")

        except Exception as e:
            self.logger.error(f"Failed to connect to Live API: {e}")
            await self._handle_connection_error(e)
            raise

    async def disconnect(self) -> None:
        """Disconnect from Live API."""
        if self.session:
            try:
                await self.session.close()
                self.is_connected = False

                await self.event_bus.emit(
                    EventType.SESSION_ENDED,
                    {"session_id": id(self.session)},
                    "session_manager"
                )

                self.logger.info("Disconnected from Gemini Live API")

            except Exception as e:
                self.logger.error(f"Error during disconnect: {e}")
            finally:
                self.session = None

    async def reconnect(self) -> bool:
        """Attempt to reconnect with session resumption."""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error("Max reconnection attempts reached")
            return False

        self.reconnect_attempts += 1
        self.logger.info(f"Attempting reconnection {self.reconnect_attempts}/{self.max_reconnect_attempts}")

        try:
            await self.disconnect()
            await asyncio.sleep(2 ** self.reconnect_attempts)  # Exponential backoff
            await self.connect()

            if self.session_handle:
                await self.event_bus.emit(
                    EventType.SESSION_RESUMED,
                    {"session_handle": self.session_handle},
                    "session_manager"
                )

            return True

        except Exception as e:
            self.logger.error(f"Reconnection attempt {self.reconnect_attempts} failed: {e}")
            return False

    def _create_session_config(self) -> types.LiveConnectConfig:
        """Create session configuration."""
        # Create basic config with supported parameters
        config = types.LiveConnectConfig(
            response_modalities=[modality.value for modality in self.config.live_api.response_modalities],
            system_instruction=self._get_system_instruction()
        )

        # Note: Advanced features like context compression and session resumption
        # may not be available in the current version of the Google GenAI library

        return config

    def _get_system_instruction(self) -> types.Content:
        """Get system instruction for the Live API session."""
        instruction = """
        You are an advanced computer control assistant with real-time visual processing capabilities.
        You can see the computer screen continuously through a live video feed and can interact with it naturally.

        Your capabilities include:
        1. Real-time visual analysis of the screen
        2. Precise element identification and interaction
        3. Dynamic decision making based on visual context
        4. Handling interruptions and priority changes
        5. Multi-modal interaction (text, audio, visual)

        Always:
        - Analyze the visual context before taking actions
        - Provide clear explanations of what you observe
        - Handle errors and unexpected situations gracefully
        - Maintain awareness of the current task and progress
        - Use the most appropriate tools for each situation
        """

        return types.Content(
            parts=[types.Part(text=instruction)]
        )

    async def _handle_connection_error(self, error: Exception) -> None:
        """Handle connection errors."""
        await self.event_bus.emit(
            EventType.API_ERROR,
            {"error": str(error), "type": "connection_error"},
            "session_manager"
        )

        # Attempt automatic reconnection for certain error types
        if "connection" in str(error).lower() or "timeout" in str(error).lower():
            asyncio.create_task(self.reconnect())

    async def send_realtime_input(self, **kwargs) -> None:
        """Send realtime input to the session."""
        if not self.session or not self.is_connected:
            raise RuntimeError("No active session")

        try:
            await self.session.send_realtime_input(**kwargs)
        except Exception as e:
            self.logger.error(f"Error sending realtime input: {e}")
            await self._handle_connection_error(e)
            raise

    async def send_client_content(self, **kwargs) -> None:
        """Send client content to the session."""
        if not self.session or not self.is_connected:
            raise RuntimeError("No active session")

        try:
            await self.session.send_client_content(**kwargs)
        except Exception as e:
            self.logger.error(f"Error sending client content: {e}")
            await self._handle_connection_error(e)
            raise

    def receive(self):
        """Get the session receive iterator."""
        if not self.session or not self.is_connected:
            raise RuntimeError("No active session")

        return self.session.receive()
