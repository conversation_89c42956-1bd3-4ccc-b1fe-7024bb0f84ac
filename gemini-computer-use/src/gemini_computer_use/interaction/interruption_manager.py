"""Interruption manager for handling visual and user interruptions."""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass

from ..core.session_manager import SessionManager
from ..core.event_bus import get_event_bus, EventType
from ..vision.visual_processor import VisualProcessor


class InterruptionType(Enum):
    """Types of interruptions."""
    USER_REQUEST = "user_request"
    ERROR_DIALOG = "error_dialog"
    POPUP_WINDOW = "popup_window"
    LOADING_COMPLETE = "loading_complete"
    NEW_CONTENT = "new_content"
    SYSTEM_NOTIFICATION = "system_notification"
    VISUAL_CHANGE = "visual_change"


@dataclass
class Interruption:
    """Interruption data structure."""
    type: InterruptionType
    priority: int  # 1 = highest, 10 = lowest
    description: str
    timestamp: float
    data: Any = None
    handled: bool = False


class InterruptionManager:
    """Manages interruptions and priority-based response handling."""
    
    def __init__(self, session_manager: SessionManager, 
                 visual_processor: VisualProcessor):
        self.session_manager = session_manager
        self.visual_processor = visual_processor
        self.event_bus = get_event_bus()
        self.logger = logging.getLogger(__name__)
        
        self.is_active = False
        self.interruption_queue = asyncio.PriorityQueue()
        self.processing_task: Optional[asyncio.Task] = None
        
        # Interruption detection patterns
        self.error_patterns = [
            "error", "failed", "exception", "warning", "alert",
            "cannot", "unable", "invalid", "denied"
        ]
        
        self.popup_patterns = [
            "dialog", "popup", "modal", "confirm", "alert",
            "notification", "message"
        ]
        
        # Priority mapping
        self.priority_map = {
            InterruptionType.USER_REQUEST: 1,
            InterruptionType.ERROR_DIALOG: 2,
            InterruptionType.POPUP_WINDOW: 3,
            InterruptionType.SYSTEM_NOTIFICATION: 4,
            InterruptionType.LOADING_COMPLETE: 5,
            InterruptionType.NEW_CONTENT: 6,
            InterruptionType.VISUAL_CHANGE: 7
        }
    
    async def start(self) -> None:
        """Start interruption management."""
        if self.is_active:
            return
        
        self.is_active = True
        self.logger.info("Starting interruption manager")
        
        # Subscribe to relevant events
        self.event_bus.subscribe(EventType.VISUAL_INTERRUPTION, self._on_visual_interruption)
        self.event_bus.subscribe(EventType.SCREEN_CHANGED, self._on_screen_changed)
        
        # Start processing task
        self.processing_task = asyncio.create_task(self._processing_loop())
    
    async def stop(self) -> None:
        """Stop interruption management."""
        if not self.is_active:
            return
        
        self.is_active = False
        self.logger.info("Stopping interruption manager")
        
        # Unsubscribe from events
        self.event_bus.unsubscribe(EventType.VISUAL_INTERRUPTION, self._on_visual_interruption)
        self.event_bus.unsubscribe(EventType.SCREEN_CHANGED, self._on_screen_changed)
        
        # Cancel processing task
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
    
    async def trigger_interruption(self, interruption_type: str, 
                                 description: str = "", 
                                 data: Any = None) -> None:
        """Manually trigger an interruption."""
        try:
            int_type = InterruptionType(interruption_type)
            await self._add_interruption(int_type, description, data)
        except ValueError:
            self.logger.warning(f"Unknown interruption type: {interruption_type}")
    
    async def _processing_loop(self) -> None:
        """Main processing loop for handling interruptions."""
        while self.is_active:
            try:
                # Wait for interruption with timeout
                try:
                    priority, interruption = await asyncio.wait_for(
                        self.interruption_queue.get(), 
                        timeout=1.0
                    )
                    
                    if not interruption.handled:
                        await self._handle_interruption(interruption)
                        interruption.handled = True
                        
                except asyncio.TimeoutError:
                    continue
                    
            except Exception as e:
                self.logger.error(f"Error in interruption processing loop: {e}")
                await asyncio.sleep(0.1)
    
    async def _on_visual_interruption(self, event) -> None:
        """Handle visual interruption events."""
        try:
            data = event.data
            interruption_type = data.get("type", "visual_change")
            
            # Map event type to interruption type
            if "error" in interruption_type.lower():
                int_type = InterruptionType.ERROR_DIALOG
            elif "popup" in interruption_type.lower():
                int_type = InterruptionType.POPUP_WINDOW
            elif "notification" in interruption_type.lower():
                int_type = InterruptionType.SYSTEM_NOTIFICATION
            else:
                int_type = InterruptionType.VISUAL_CHANGE
            
            await self._add_interruption(
                int_type, 
                f"Visual interruption: {interruption_type}",
                data
            )
            
        except Exception as e:
            self.logger.error(f"Error handling visual interruption event: {e}")
    
    async def _on_screen_changed(self, event) -> None:
        """Handle screen change events."""
        try:
            # Analyze screen change for potential interruptions
            current_context = self.visual_processor.get_current_context()
            
            if current_context:
                # Check for error patterns
                if any(pattern in current_context.screen_state.lower() 
                       for pattern in self.error_patterns):
                    await self._add_interruption(
                        InterruptionType.ERROR_DIALOG,
                        "Potential error detected on screen",
                        {"context": current_context}
                    )
                
                # Check for popup patterns
                elif any(pattern in current_context.screen_state.lower() 
                         for pattern in self.popup_patterns):
                    await self._add_interruption(
                        InterruptionType.POPUP_WINDOW,
                        "Potential popup detected on screen",
                        {"context": current_context}
                    )
                
        except Exception as e:
            self.logger.error(f"Error handling screen change event: {e}")
    
    async def _add_interruption(self, interruption_type: InterruptionType,
                              description: str, data: Any = None) -> None:
        """Add an interruption to the queue."""
        try:
            interruption = Interruption(
                type=interruption_type,
                priority=self.priority_map.get(interruption_type, 5),
                description=description,
                timestamp=time.time(),
                data=data
            )
            
            # Add to priority queue (lower number = higher priority)
            await self.interruption_queue.put((interruption.priority, interruption))
            
            self.logger.info(f"Added interruption: {interruption_type.value} - {description}")
            
        except Exception as e:
            self.logger.error(f"Error adding interruption: {e}")
    
    async def _handle_interruption(self, interruption: Interruption) -> None:
        """Handle a specific interruption."""
        try:
            self.logger.info(f"Handling interruption: {interruption.type.value}")
            
            # Create interruption message for the model
            interruption_message = self._create_interruption_message(interruption)
            
            # Send interruption message to Live API
            await self.session_manager.send_client_content(
                turns={
                    "role": "user",
                    "parts": [{"text": interruption_message}]
                },
                turn_complete=True
            )
            
            # Log the interruption handling
            self.logger.debug(f"Sent interruption message: {interruption_message[:100]}...")
            
        except Exception as e:
            self.logger.error(f"Error handling interruption {interruption.type.value}: {e}")
    
    def _create_interruption_message(self, interruption: Interruption) -> str:
        """Create a message to send to the model about the interruption."""
        base_message = f"""
        INTERRUPTION DETECTED: {interruption.type.value.upper()}
        
        Description: {interruption.description}
        Priority: {interruption.priority} (1=highest, 10=lowest)
        Time: {time.strftime('%H:%M:%S', time.localtime(interruption.timestamp))}
        
        """
        
        # Add specific handling instructions based on interruption type
        if interruption.type == InterruptionType.ERROR_DIALOG:
            base_message += """
            This appears to be an error or warning dialog. Please:
            1. Analyze the error message carefully
            2. Determine if this affects the current task
            3. Take appropriate action (dismiss, retry, or adjust approach)
            4. Continue with the task if possible
            """
            
        elif interruption.type == InterruptionType.POPUP_WINDOW:
            base_message += """
            A popup or dialog window has appeared. Please:
            1. Read the popup content
            2. Determine the appropriate response
            3. Handle the popup (accept, dismiss, or fill out)
            4. Continue with the main task
            """
            
        elif interruption.type == InterruptionType.USER_REQUEST:
            base_message += """
            The user has made a request that requires immediate attention.
            Please prioritize this request and respond accordingly.
            """
            
        elif interruption.type == InterruptionType.LOADING_COMPLETE:
            base_message += """
            A loading process has completed. Please:
            1. Check the new state of the screen
            2. Verify if the expected content has loaded
            3. Continue with the next step of the task
            """
            
        else:
            base_message += """
            Please analyze the current screen state and determine if any action is needed
            before continuing with the current task.
            """
        
        return base_message
    
    def get_queue_size(self) -> int:
        """Get the current size of the interruption queue."""
        return self.interruption_queue.qsize()
    
    def is_processing_interruptions(self) -> bool:
        """Check if interruption processing is active."""
        return self.is_active and self.processing_task is not None
